{% extends "base.html" %}

{% block content %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">

<div class="container-fluid dashboard-container">


    <!-- Quick Access Section -->
    <div class="quick-access-section{% if not current_user.subscription or current_user.subscription.name == 'Free' %} mt-0{% endif %}" style="margin-bottom: 10px;">
        <!-- Header removed as requested -->
        <div class="quick-access-grid">
            <a href="{{ url_for('pos.pos') }}" class="quick-access-card pos">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-shopping-cart" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>POS</span>
                </div>
            </a>
            <a href="{{ url_for('inventory.inventory_dashboard') }}" class="quick-access-card inventory">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-boxes" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Inventory</span>
                </div>
            </a>

            <a href="{{ url_for('shopify_customers.get_customers') }}" class="quick-access-card customers">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-user-friends" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Customers</span>
                </div>
            </a>
            <a href="{{ url_for('buylist.buylist_dashboard') }}" class="quick-access-card buylist">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="far fa-credit-card" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Buylist</span>
                </div>
            </a>
            <a href="{{ url_for('shopify_autopricing.index') }}" class="quick-access-card pricing">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px;">
                    <i class="fas fa-dollar-sign" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Pricing</span>
                </div>
            </a>

            {% if current_user.username == 'admintcg' %}
            <a href="{{ url_for('admin.admin_dashboard') }}" class="quick-access-card admin">
                <div class="quick-access-card-icon" style="width: 50px; height: 50px; position: relative;">
                    <i class="fas fa-user-shield" style="font-size: 28px;"></i>
                </div>
                <div class="quick-access-card-content">
                    <span>Admin Panel</span>
                </div>
            </a>
            {% endif %}
        </div>
    </div>

    <!-- Tabbed Dashboard Section -->
    <div class="dashboard-card tabbed-section">
        <div class="dashboard-card-header d-flex justify-content-between align-items-center py-2">
            <div>
                <i class="fas fa-chart-line me-1" style="color: #4CAF50;"></i>
                <span style="font-size: 0.9rem;">Dashboard</span>
            </div>
        </div>

        <!-- Tabs Navigation - Hidden since there's only one tab -->
        <div class="dashboard-tabs" style="display: none;">
            <button class="dashboard-tab-btn active" data-tab="yesterday">Yesterday</button>
        </div>

        <div class="dashboard-card-body py-3">


            <!-- Yesterday Tab Content -->
            <div class="dashboard-tab-content active" id="yesterday-tab">
                <div class="row">
                    <div class="col-md-12">
                        <div class="order-stats-container">
                            <div class="order-stat-card">
                                <div class="order-stat-icon">
                                    <i class="fas fa-shopping-cart" style="color: #4CAF50;"></i>
                                </div>
                                <div class="order-stat-content">
                                    <h3 class="order-stat-value">{{ last_24_hours_orders }}</h3>
                                    <p class="order-stat-label">Total Orders</p>
                                </div>
                            </div>

                            <div class="order-stat-card">
                                <div class="order-stat-icon">
                                    <i class="fas fa-dollar-sign" style="color: #2196F3;"></i>
                                </div>
                                <div class="order-stat-content">
                                    <h3 class="order-stat-value">{{ last_24_hours_value|round(2) }}</h3>
                                    <p class="order-stat-label">Total Value</p>
                                </div>
                            </div>

                            <div class="order-stat-card">
                                <div class="order-stat-icon">
                                    <i class="fas fa-calculator" style="color: #FF9800;"></i>
                                </div>
                                <div class="order-stat-content">
                                    <h3 class="order-stat-value">
                                        {% if last_24_hours_orders > 0 %}
                                            {{ (last_24_hours_value / last_24_hours_orders)|round(2) }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </h3>
                                    <p class="order-stat-label">Average Order Value</p>
                                </div>
                            </div>
                        </div>

                        <div class="top-product-section mt-4">
                            <h5 class="section-title"><i class="fas fa-trophy me-2" style="color: #FFC107;"></i>Top Selling Product</h5>
                            {% if top_product %}
                            <div class="top-product-card">
                                <div class="top-product-info">
                                    <h4 class="product-name">{{ top_product.name }}</h4>
                                    <div class="product-details">
                                        <span class="product-vendor">{{ top_product.vendor }}</span>
                                        {% if top_product.variant %}
                                        <span class="product-variant">{{ top_product.variant }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="top-product-stats">
                                    <div class="stat-item">
                                        <span class="stat-value">{{ top_product.quantity }}</span>
                                        <span class="stat-label">Units Sold</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">{{ top_product.price }}</span>
                                        <span class="stat-label">Unit Price</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value">{{ (top_product.quantity * top_product.price|float)|round(2) }}</span>
                                        <span class="stat-label">Total Revenue</span>
                                    </div>
                                    <div class="stat-item stock-level">
                                        <span class="stat-value {% if top_product.current_stock < 5 %}low-stock{% elif top_product.current_stock < 10 %}medium-stock{% else %}good-stock{% endif %}">
                                            {{ top_product.current_stock }}
                                        </span>
                                        <span class="stat-label">Current Stock</span>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="no-data-message">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>No product sales data available for the last 24 hours.</span>
                            </div>
                            {% endif %}
                        </div>

                        <div class="alert alert-info mt-3 mb-0" style="background-color: rgba(33, 150, 243, 0.1); border-color: rgba(33, 150, 243, 0.2); color: #2196F3;">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>These statistics show your store's performance over the last 24 hours. Check back regularly to monitor your sales trends.</span>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- Additional content can be added here in the future -->

</div>

<!-- Include favorites.js for handling favorites functionality -->
<script src="{{ url_for('static', filename='js/favorites.js') }}"></script>

<!-- Include Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>

<!-- Include dashboard.js for handling dashboard functionality -->
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>

<script>
        // Initialize quick access section functionality
        initQuickAccessSection();
</script>
{% endblock %}
