{% extends "base.html" %}

{% block title %}Pricing - Coming Soon{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-body text-center p-5">
                    <i class="fas fa-dollar-sign fa-4x mb-4 text-success"></i>
                    <h2 class="mb-3">Pricing Management</h2>
                    <p class="lead mb-4">Our comprehensive pricing management system is coming soon!</p>
                    <p class="text-muted mb-4">This feature will allow you to manage pricing strategies, bulk price updates, and automated pricing rules.</p>
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="feature-preview">
                                <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                                <h5>Dynamic Pricing</h5>
                                <p class="small text-muted">Automated pricing based on market conditions</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-preview">
                                <i class="fas fa-tags fa-2x text-warning mb-2"></i>
                                <h5>Bulk Updates</h5>
                                <p class="small text-muted">Update prices across multiple products</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-preview">
                                <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                                <h5>Pricing Rules</h5>
                                <p class="small text-muted">Set up automated pricing strategies</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="{{ url_for('dashboard.dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Return to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-preview {
    padding: 20px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.feature-preview h5 {
    color: #333;
    margin-bottom: 10px;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
}
</style>
{% endblock %}
