from flask import Blueprint, request, jsonify, current_app
from bson.json_util import dumps
import json
from models.user_model import User
from middlewares.auth_middleware import login_required
from flask_login import current_user
import logging

shopify_autopricing_bp = Blueprint('shopify_autopricing', __name__)
logger = logging.getLogger(__name__)

@shopify_autopricing_bp.route('/api/games', methods=['GET'])
@login_required
def get_games():
    """Get a list of all games in the system"""
    try:
        # Get games from catalog collection
        db = current_app.mongo_client.get_database()
        catalog = db.catalog

        # Find distinct game values
        games = catalog.distinct('game')

        # Filter out None values and sort alphabetically
        games = sorted([game for game in games if game], key=lambda x: x.lower())

        return jsonify({
            'success': True,
            'games': games
        })
    except Exception as e:
        logger.error(f"Error getting games: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get games: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/game-data/<game_name>', methods=['GET'])
@login_required
def get_game_data(game_name):
    """Get game-specific data like rarities and print types"""
    try:
        db = current_app.mongo_client.get_database()
        
        # Get rarities and print types from catalog
        catalog = db.catalog
        
        # Find distinct rarities for this game
        rarities = catalog.distinct('rarity', {'game': game_name})
        rarities = [r for r in rarities if r]
        
        # Find distinct print types for this game
        print_types = catalog.distinct('printing', {'game': game_name})
        print_types = [pt for pt in print_types if pt]
        
        # Check if we have rarity data in the rarities collection
        rarities_collection = db.rarities
        rarity_data = list(rarities_collection.find({'game': game_name}))
        
        # Check if we have printing data in the printings collection
        printings_collection = db.printings
        printing_data = list(printings_collection.find({'game': game_name}))
        
        return jsonify({
            'success': True,
            'rarities': rarities,
            'print_types': print_types,
            'rarity_data': json.loads(dumps(rarity_data)),
            'printing_data': json.loads(dumps(printing_data))
        })
    except Exception as e:
        logger.error(f"Error getting game data for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get game data: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/game-rules/<game_name>', methods=['GET'])
@login_required
def get_game_rules(game_name):
    """Get game-specific autopricing rules"""
    try:
        # Get game rules from user's settings
        game_rules = current_user.shopify_settings.get('game_rules', {}).get(game_name, {})
        
        return jsonify({
            'success': True,
            **game_rules
        })
    except Exception as e:
        logger.error(f"Error getting game rules for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get game rules: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/game-rules/<game_name>', methods=['POST'])
@login_required
def save_game_rules(game_name):
    """Save game-specific autopricing rules"""
    try:
        data = request.json

        # Initialize game_rules if it doesn't exist
        if 'game_rules' not in current_user.shopify_settings:
            current_user.shopify_settings['game_rules'] = {}

        # Save the game rules
        current_user.shopify_settings['game_rules'][game_name] = data
        current_user.save()
        
        return jsonify({
            'success': True,
            'message': f"Game rules for {game_name} saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving game rules for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to save game rules: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/game-rules/<game_name>', methods=['DELETE'])
@login_required
def delete_game_rules(game_name):
    """Delete game-specific autopricing rules"""
    try:
        # Remove the game rules if they exist
        if 'game_rules' in current_user.shopify_settings and game_name in current_user.shopify_settings['game_rules']:
            del current_user.shopify_settings['game_rules'][game_name]
            current_user.save()
        
        return jsonify({
            'success': True,
            'message': f"Game rules for {game_name} deleted successfully"
        })
    except Exception as e:
        logger.error(f"Error deleting game rules for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to delete game rules: {str(e)}"
        }), 500

# Legacy endpoint for backward compatibility
@shopify_autopricing_bp.route('/api/game-minimum-prices/<game_name>', methods=['GET'])
@login_required
def get_game_minimum_prices(game_name):
    """Get game-specific minimum prices (legacy endpoint)"""
    try:
        # Get game rules from user's settings
        game_rules = current_user.shopify_settings.get('game_rules', {}).get(game_name, {})
        
        # Extract minimum price data for backward compatibility
        minimum_prices = {
            'print_types': game_rules.get('print_types', {}),
            'rarities': game_rules.get('rarities', {}),
            'default_min_price': game_rules.get('default_min_price')
        }
        
        return jsonify({
            'success': True,
            **minimum_prices
        })
    except Exception as e:
        logger.error(f"Error getting game minimum prices for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get game minimum prices: {str(e)}"
        }), 500

# Legacy endpoint for backward compatibility
@shopify_autopricing_bp.route('/api/game-minimum-prices/<game_name>', methods=['POST'])
@login_required
def save_game_minimum_prices(game_name):
    """Save game-specific minimum prices (legacy endpoint)"""
    try:
        data = request.json

        # Initialize game_rules if it doesn't exist
        if 'game_rules' not in current_user.shopify_settings:
            current_user.shopify_settings['game_rules'] = {}

        # Initialize this game's rules if they don't exist
        if game_name not in current_user.shopify_settings['game_rules']:
            current_user.shopify_settings['game_rules'][game_name] = {}

        # Update minimum price data
        current_user.shopify_settings['game_rules'][game_name]['print_types'] = data.get('print_types', {})
        current_user.shopify_settings['game_rules'][game_name]['rarities'] = data.get('rarities', {})
        current_user.shopify_settings['game_rules'][game_name]['default_min_price'] = data.get('default_min_price')

        current_user.save()
        
        return jsonify({
            'success': True,
            'message': f"Game minimum prices for {game_name} saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving game minimum prices for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to save game minimum prices: {str(e)}"
        }), 500

# Legacy endpoint for backward compatibility
@shopify_autopricing_bp.route('/api/game-minimum-prices/<game_name>', methods=['DELETE'])
@login_required
def delete_game_minimum_prices(game_name):
    """Delete game-specific minimum prices (legacy endpoint)"""
    try:
        # Remove the game rules if they exist
        if 'game_rules' in current_user.shopify_settings and game_name in current_user.shopify_settings['game_rules']:
            del current_user.shopify_settings['game_rules'][game_name]
            current_user.save()
        
        return jsonify({
            'success': True,
            'message': f"Game minimum prices for {game_name} deleted successfully"
        })
    except Exception as e:
        logger.error(f"Error deleting game minimum prices for {game_name}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to delete game minimum prices: {str(e)}"
        }), 500

# General autopricing settings endpoints
@shopify_autopricing_bp.route('/api/settings', methods=['GET'])
@login_required
def get_autopricing_settings():
    """Get general autopricing settings"""
    try:
        # Get autopricing settings
        settings = current_user.shopify_settings.get('autopricing', {})
        
        return jsonify({
            'success': True,
            **settings
        })
    except Exception as e:
        logger.error(f"Error getting autopricing settings: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get autopricing settings: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/settings', methods=['POST'])
@login_required
def save_autopricing_settings():
    """Save general autopricing settings"""
    try:
        data = request.json

        # Save autopricing settings
        current_user.shopify_settings['autopricing'] = data
        current_user.save()
        
        return jsonify({
            'success': True,
            'message': "Autopricing settings saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving autopricing settings: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to save autopricing settings: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/price-type-preference', methods=['GET'])
@login_required
def get_price_type_preference():
    """Get price type preference order"""
    try:
        # Get price type preference
        preference = current_user.shopify_settings.get('price_type_preference', [])
        
        return jsonify({
            'success': True,
            'price_type_order': preference
        })
    except Exception as e:
        logger.error(f"Error getting price type preference: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get price type preference: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/price-type-preference', methods=['POST'])
@login_required
def save_price_type_preference():
    """Save price type preference order"""
    try:
        data = request.json

        # Save price type preference
        current_user.shopify_settings['price_type_preference'] = data.get('price_type_order', [])
        current_user.save()
        
        return jsonify({
            'success': True,
            'message': "Price type preference saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving price type preference: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to save price type preference: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/price-comparison', methods=['GET'])
@login_required
def get_price_comparison_settings():
    """Get price comparison settings"""
    try:
        # Get price comparison settings
        settings = current_user.shopify_settings.get('price_comparison', {})
        
        return jsonify({
            'success': True,
            **settings
        })
    except Exception as e:
        logger.error(f"Error getting price comparison settings: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to get price comparison settings: {str(e)}"
        }), 500

@shopify_autopricing_bp.route('/api/price-comparison', methods=['POST'])
@login_required
def save_price_comparison_settings():
    """Save price comparison settings"""
    try:
        data = request.json

        # Save price comparison settings
        current_user.shopify_settings['price_comparison'] = data
        current_user.save()
        
        return jsonify({
            'success': True,
            'message': "Price comparison settings saved successfully"
        })
    except Exception as e:
        logger.error(f"Error saving price comparison settings: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to save price comparison settings: {str(e)}"
        }), 500

# Test settings endpoint
@shopify_autopricing_bp.route('/products/api/test_settings', methods=['POST'])
@login_required
def test_settings():
    """Test autopricing settings on a random product"""
    try:
        db = current_app.mongo_client.get_database()
        
        # Get a random product with variants
        shopify_products = db.shopify_products
        product = shopify_products.aggregate([
            {'$match': {'user_id': str(current_user.id), 'variants': {'$exists': True, '$ne': []}}},
            {'$sample': {'size': 1}}
        ]).next()
        
        if not product:
            return jsonify({
                'success': False,
                'error': "No products found to test settings on"
            }), 404
        
        # Simulate price changes for each variant
        price_changes = []
        for variant in product.get('variants', []):
            # Get current price
            old_price = float(variant.get('price', 0))
            
            # Simulate new price (for demonstration purposes)
            # In a real implementation, this would use the actual pricing logic
            new_price = old_price * 1.1  # 10% increase for demonstration
            
            # Calculate difference
            difference = new_price - old_price
            difference_percent = round((difference / old_price) * 100) if old_price > 0 else 0
            
            price_changes.append({
                'variant_title': variant.get('title', 'Unknown'),
                'old_price': old_price,
                'new_price': new_price,
                'difference': difference,
                'difference_percent': difference_percent
            })
        
        return jsonify({
            'success': True,
            'product': {
                'title': product.get('title', 'Unknown Product'),
                'image': product.get('image', {}).get('src') if product.get('image') else None
            },
            'price_changes': price_changes
        })
    except Exception as e:
        logger.error(f"Error testing settings: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Failed to test settings: {str(e)}"
        }), 500
