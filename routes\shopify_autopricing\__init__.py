from flask import Blueprint
from .pricing_settings import create_pricing_settings_routes
from .games import create_games_routes
from .main import create_main_routes
from .api_wrappers import save_game_wrapper, get_price_type_preference_wrapper, save_price_type_preference_wrapper

def create_autopricing_bp():
    """Create the main autopricing blueprint."""
    autopricing_bp = Blueprint('shopify_autopricing', __name__, url_prefix='/shopify/autopricing')

    # Register main routes
    create_main_routes(autopricing_bp)

    # Register pricing settings routes
    create_pricing_settings_routes(autopricing_bp)

    # Register games routes
    create_games_routes(autopricing_bp)

    # Register API routes from shopify_autopricing_routes.py with deferred import
    def register_api_routes():
        try:
            from routes import shopify_autopricing_routes

            # Register the API routes directly
            autopricing_bp.add_url_rule('/api/games', 'get_games', shopify_autopricing_routes.get_games, methods=['GET'])
            autopricing_bp.add_url_rule('/api/game-data/<game_name>', 'get_game_data', shopify_autopricing_routes.get_game_data, methods=['GET'])
            autopricing_bp.add_url_rule('/api/game-rules/<game_name>', 'get_game_rules', shopify_autopricing_routes.get_game_rules, methods=['GET'])
            autopricing_bp.add_url_rule('/api/game-rules/<game_name>', 'save_game_rules', shopify_autopricing_routes.save_game_rules, methods=['POST'])
            autopricing_bp.add_url_rule('/api/game-rules/<game_name>', 'delete_game_rules', shopify_autopricing_routes.delete_game_rules, methods=['DELETE'])
            autopricing_bp.add_url_rule('/api/game-minimum-prices/<game_name>', 'get_game_minimum_prices', shopify_autopricing_routes.get_game_minimum_prices, methods=['GET'])
            autopricing_bp.add_url_rule('/api/game-minimum-prices/<game_name>', 'save_game_minimum_prices', shopify_autopricing_routes.save_game_minimum_prices, methods=['POST'])
            autopricing_bp.add_url_rule('/api/game-minimum-prices/<game_name>', 'delete_game_minimum_prices', shopify_autopricing_routes.delete_game_minimum_prices, methods=['DELETE'])
            autopricing_bp.add_url_rule('/api/settings', 'get_autopricing_settings', shopify_autopricing_routes.get_autopricing_settings, methods=['GET'])
            autopricing_bp.add_url_rule('/api/settings', 'save_autopricing_settings', shopify_autopricing_routes.save_autopricing_settings, methods=['POST'])
            autopricing_bp.add_url_rule('/api/price-type-preference', 'get_price_type_preference', shopify_autopricing_routes.get_price_type_preference, methods=['GET'])
            autopricing_bp.add_url_rule('/api/price-type-preference', 'save_price_type_preference', shopify_autopricing_routes.save_price_type_preference, methods=['POST'])
            autopricing_bp.add_url_rule('/api/price-comparison', 'get_price_comparison_settings', shopify_autopricing_routes.get_price_comparison_settings, methods=['GET'])
            autopricing_bp.add_url_rule('/api/price-comparison', 'save_price_comparison_settings', shopify_autopricing_routes.save_price_comparison_settings, methods=['POST'])
            autopricing_bp.add_url_rule('/products/api/test_settings', 'test_settings', shopify_autopricing_routes.test_settings, methods=['POST'])

            # Add alias routes for JavaScript compatibility
            autopricing_bp.add_url_rule('/api/price-preference', 'get_price_preference_alias', get_price_type_preference_wrapper, methods=['GET'])
            autopricing_bp.add_url_rule('/api/price-preference', 'save_price_preference_alias', save_price_type_preference_wrapper, methods=['POST'])
            autopricing_bp.add_url_rule('/api/game/<game_name>', 'get_game_alias', shopify_autopricing_routes.get_game_rules, methods=['GET'])
            autopricing_bp.add_url_rule('/api/game', 'save_game_alias', save_game_wrapper, methods=['POST'])
            autopricing_bp.add_url_rule('/api/game/<game_name>', 'delete_game_alias', shopify_autopricing_routes.delete_game_rules, methods=['DELETE'])

        except ImportError as e:
            # If import fails, log the error but don't crash the blueprint creation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to import shopify_autopricing_routes: {e}")

    # Call the function to register routes
    register_api_routes()

    return autopricing_bp
