from flask import Blueprint, render_template, request, jsonify, send_file
from flask_login import login_required, current_user
from middlewares.auth_middleware import subscription_required
from utils.currency_converter import get_currency_symbol
from pymongo import MongoClient, ASCENDING
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, NetworkTimeout
from datetime import datetime, timedelta, timezone
import time
from collections import defaultdict, Counter
import csv
import io
import logging
import re
import os
import openai
import json
import traceback
from bson import json_util, ObjectId
import traceback
import ast
import requests
from .comics_routes import comics_scanning_bp
from .cardmarket_routes import cardmarket_bp
from .eu_purchasing_routes import eu_purchasing_bp  # Updated import to match blueprint name
from .shopify_export_routes import shopify_export_bp
from routes.admin_routes import ensure_user_subscription
from routes.ebay_auth_routes import refresh_ebay_token_if_needed
from routes.ticket_routes import count_open_tickets
from functools import wraps, lru_cache
from models.user_model import User, Subscription
from mongoengine.errors import DoesNotExist

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API Keys (Replace these with secure environment variables in production)
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', '********************************************************')

# Initialize OpenAI API key
openai.api_key = OPENAI_API_KEY

# MongoDB Configuration with robust connection handling
mongo_uri = 'mongodb://admin:Reggie2805!@*************:27017/?authSource=admin'
mongo_dbname = 'test'

def get_mongo_connection():
    """Get MongoDB connection with robust error handling"""
    try:
        # Conservative connection settings to avoid timeouts
        mongo_client = MongoClient(
            mongo_uri,
            serverSelectionTimeoutMS=15000,  # 15 seconds - reduced for faster failure
            connectTimeoutMS=15000,          # 15 seconds - reduced for faster failure
            socketTimeoutMS=30000,           # 30 seconds - reduced from 60
            maxIdleTimeMS=60000,             # 1 minute - reduced from 5 minutes
            heartbeatFrequencyMS=30000,      # 30 seconds - increased from 10
            maxPoolSize=10,                  # Reduced from 50 to avoid overwhelming server
            minPoolSize=1,                   # Minimum connections
            maxConnecting=3,                 # Limit concurrent connections
            retryWrites=True,
            retryReads=True
        )

        # Test connection with a quick ping
        mongo_client.admin.command('ping')
        return mongo_client[mongo_dbname]

    except Exception as e:
        logger.error(f"MongoDB connection failed: {str(e)}")
        # Return None to indicate connection failure
        return None

def execute_db_operation(operation, max_retries=2):
    """Execute database operation with retry logic"""
    for attempt in range(max_retries):
        try:
            db = get_mongo_connection()
            if db is None:
                raise ConnectionFailure("Unable to connect to MongoDB")

            return operation(db)

        except Exception as e:
            logger.warning(f"Database operation failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(2)  # Wait before retry
            else:
                logger.error("All database operation attempts failed")
                raise

# Initialize database connection
db = get_mongo_connection()
shOrders_collection = db['shOrders']
catalog_collection = db['catalog']
tickets_collection = db['tickets']
shProducts_collection = db['shProducts']
prices_collection = db['prices']
saleshistories_collection = db['saleshistories']
wallet_collection = db['wallet']

updates_collection = db['updates']
staged_inventory_collection = db['staged_inventory']

dashboard_bp = Blueprint('dashboard', __name__)
dashboard_bp.register_blueprint(comics_scanning_bp)
dashboard_bp.register_blueprint(cardmarket_bp, url_prefix='/cardmarket')
dashboard_bp.register_blueprint(eu_purchasing_bp, url_prefix='/eu-purchasing')
dashboard_bp.register_blueprint(shopify_export_bp, url_prefix='/shopify-export')

@dashboard_bp.route('/pricing')
@login_required
def pricing():
    """Pricing holding page - coming soon"""
    return render_template('pricing_holding.html')

def check_subscription(f):
    """Decorator to ensure user has a valid subscription"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_authenticated:
            try:
                # Get user object from database to ensure we have latest data
                user_obj = User.objects(username=current_user.username).first()
                if not user_obj:
                    logger.error(f"User not found in check_subscription: {current_user.username}")
                    return render_template('error.html', error_message="User not found")

                # Get subscription name before ensuring subscription
                old_subscription = user_obj.get_subscription_name()

                # Ensure subscription is valid
                user_obj = ensure_user_subscription(user_obj)

                # Get new subscription name after ensuring subscription
                new_subscription = user_obj.get_subscription_name()

                # Log if subscription changed
                if old_subscription != new_subscription:
                    logger.info(f"Subscription changed for user {user_obj.username}: {old_subscription} -> {new_subscription}")

                # Verify subscription reference is valid
                if user_obj.subscription:
                    try:
                        subscription = Subscription.objects.get(id=user_obj.subscription.id)
                        if subscription.name != user_obj.subscription.name:
                            logger.warning(f"Subscription name mismatch for user {user_obj.username}: {user_obj.subscription.name} != {subscription.name}")
                            # If current subscription is Lifetime but reference is wrong type, create new Lifetime
                            if user_obj.subscription.name == 'Lifetime':
                                try:
                                    new_subscription = Subscription(name='Lifetime').save()
                                    user_obj.subscription = new_subscription
                                    user_obj.free_subscription = None
                                    user_obj.save()
                                    logger.info(f"Created new Lifetime subscription for user {user_obj.username} due to type mismatch")
                                except Exception as e:
                                    logger.error(f"Error creating new Lifetime subscription: {str(e)}")
                                    logger.error(traceback.format_exc())
                                    user_obj.subscription = None
                                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                                    user_obj.save()
                                    logger.info(f"Fallback to free subscription after Lifetime creation error")
                            else:
                                # For non-Lifetime subscriptions, use the correct subscription from database
                                user_obj.subscription = subscription
                                # Clear any lingering free subscription
                                if user_obj.free_subscription:
                                    user_obj.free_subscription = None
                                    logger.info(f"Cleared lingering free subscription for paid user {user_obj.username}")
                                user_obj.save()
                            logger.info(f"Fixed subscription name mismatch for user {user_obj.username}")
                    except DoesNotExist:
                        logger.error(f"Invalid subscription reference for user {user_obj.username}")
                        # If subscription was supposed to be Lifetime, create new one
                        if user_obj.subscription.name == 'Lifetime':
                            try:
                                subscription = Subscription(name='Lifetime').save()
                                user_obj.subscription = subscription
                                user_obj.free_subscription = None
                                user_obj.save()
                                logger.info(f"Recreated Lifetime subscription for user {user_obj.username}")
                            except Exception as e:
                                logger.error(f"Error recreating Lifetime subscription: {str(e)}")
                                logger.error(traceback.format_exc())
                                user_obj.subscription = None
                                user_obj.free_subscription = {'start_date': datetime.utcnow()}
                                user_obj.save()
                                logger.info(f"Fallback to free subscription after Lifetime recreation error")
                        else:
                            # For non-Lifetime subscriptions with invalid reference, fallback to free
                            user_obj.subscription = None
                            user_obj.free_subscription = {'start_date': datetime.utcnow()}
                            user_obj.save()
                            logger.info(f"Set up free subscription for user {user_obj.username} after invalid reference")
                        logger.info(f"Fixed invalid subscription reference for user {user_obj.username}")
                    except Exception as e:
                        logger.error(f"Unexpected error verifying subscription: {str(e)}")
                        logger.error(traceback.format_exc())
                        # Ensure user has at least a free subscription
                        user_obj.subscription = None
                        user_obj.free_subscription = {'start_date': datetime.utcnow()}
                        user_obj.save()
                        logger.info(f"Fallback to free subscription after unexpected error")
                elif user_obj.free_subscription and not isinstance(user_obj.free_subscription, dict):
                    # Fix invalid free_subscription format
                    logger.warning(f"Invalid free_subscription format for user {user_obj.username}")
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Fixed invalid free_subscription format for user {user_obj.username}")

                # Update current_user with latest subscription data
                current_user.subscription = user_obj.subscription
                current_user.free_subscription = user_obj.free_subscription

                # Log subscription status
                logger.info(f"Subscription check passed for user {user_obj.username}: {new_subscription}")

                # Save any changes back to the database
                try:
                    user_obj.save()
                except Exception as save_error:
                    logger.error(f"Error saving user subscription changes: {str(save_error)}")
                    logger.error(traceback.format_exc())
                    # Continue with request even if save fails

            except Exception as e:
                logger.error(f"Error in check_subscription for user {current_user.username}: {str(e)}")
                logger.error(traceback.format_exc())
                # Continue with request but ensure user has at least a free subscription
                if not current_user.free_subscription:
                    current_user.subscription = None
                    current_user.free_subscription = {'start_date': datetime.utcnow()}
                    try:
                        # Try to save the free subscription
                        user_obj = User.objects(username=current_user.username).first()
                        if user_obj:
                            user_obj.subscription = None
                            user_obj.free_subscription = {'start_date': datetime.utcnow()}
                            user_obj.save()
                            logger.info(f"Created fallback free subscription for user {current_user.username}")
                    except Exception as save_error:
                        logger.error(f"Error saving fallback free subscription: {str(save_error)}")
                        logger.error(traceback.format_exc())
        return f(*args, **kwargs)
    return decorated_function

def check_ebay_token(f):
    """Decorator to ensure eBay token is refreshed"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.is_authenticated and hasattr(current_user, 'ebay_active') and current_user.ebay_active:
            try:
                refresh_ebay_token_if_needed()
            except Exception as e:
                logger.error(f"Error refreshing eBay token: {str(e)}")
                # Continue with request even if refresh fails
        return f(*args, **kwargs)
    return decorated_function

# Cache functions
@lru_cache(maxsize=128)
def get_cached_user_data(username, timestamp):
    # timestamp is used to invalidate cache every 5 minutes
    user = db['user'].find_one({"username": username})
    return {
        'shopify_connected': bool(user and user.get('shopifyAccessToken')),
        'auto_update_status': bool(db['autoUpdate'].find_one({"username": username})),
        'auto_pricing_status': bool(db['autopricerShopify'].find_one({"username": username}))
    }

@lru_cache(maxsize=128)
def get_cached_counts(username, timestamp):
    """
    Get cached counts and statistics for the dashboard
    Uses timestamp to invalidate cache every minute
    Optimized MongoDB queries with proper indexing and projection
    """
    # Use a dictionary to store all results
    results = {
        'staged_count': 0,
        'pending_buylist_orders': 0,
        'mobile_uploads_count': 0,
        'last_24_hours_orders': 0,
        'last_24_hours_value': 0,
        'top_product': None,
        'inventory_stats': [],
        'grand_total_products': 0,
        'grand_total_quantity': 0,
        'grand_total_value': 0
    }
    
    # Get staged inventory count with projection to minimize data transfer
    try:
        # Use count_documents with a hint to use the username index
        results['staged_count'] = staged_inventory_collection.count_documents(
            {"username": username},
            hint="username_1"  # Assuming an index exists on username field
        )
    except Exception as e:
        logger.error(f"Error counting staged inventory: {str(e)}")
        results['staged_count'] = 0

    # Get mobile uploads count
    try:
        uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'mobile_uploads', username)
        if os.path.exists(uploads_dir):
            # Use list comprehension with file extension check
            results['mobile_uploads_count'] = len([
                f for f in os.listdir(uploads_dir)
                if f.lower().endswith(('.png', '.jpg', '.jpeg'))
            ])
    except Exception as e:
        logger.error(f"Error counting mobile uploads: {str(e)}")
        results['mobile_uploads_count'] = 0

    # Get pending buylist orders with robust error handling
    def get_pending_orders(db):
        return db['buylistOrders'].count_documents(
            {
                "username": username,
                "orderStatus": {"$nin": ["Complete", "amendment", "accepted"]},
                "$or": [
                    {"isComplete": False},
                    {"isComplete": {"$exists": False}}
                ]
            }
        )

    try:
        results['pending_buylist_orders'] = execute_db_operation(get_pending_orders)
    except Exception as e:
        logger.error(f"Error counting pending buylist orders: {str(e)}")
        results['pending_buylist_orders'] = 0

    # Get orders from the last 24 hours
    last_24_hours = datetime.utcnow() - timedelta(days=1)
    last_24_hours_iso = last_24_hours.strftime('%Y-%m-%dT%H:%M:%S-00:00')

    # Combine multiple aggregations into a single pipeline for better performance
    try:
        # Combined pipeline for orders, value, and top product
        combined_pipeline = [
            # Stage 1: Match orders for this user in the last 24 hours
            {"$match": {
                "username": username,
                "created_at": {"$gte": last_24_hours_iso}
            }},
            
            # Stage 2: Group by order ID to avoid duplicates
            {"$group": {
                "_id": "$id",
                "total_price": {"$first": {"$toDouble": "$total_price"}},
                "line_items": {"$first": "$line_items"}
            }},
            
            # Stage 3: Facet to perform multiple aggregations in one query
            {"$facet": {
                # Count unique orders
                "order_count": [
                    {"$count": "count"}
                ],
                
                # Calculate total value
                "total_value": [
                    {"$group": {
                        "_id": None,
                        "value": {"$sum": "$total_price"}
                    }}
                ],
                
                # Process line items for top product
                "line_items": [
                    {"$unwind": "$line_items"},
                    {"$group": {
                        "_id": "$line_items.name",
                        "total_quantity": {"$sum": "$line_items.quantity"},
                        "vendor": {"$first": "$line_items.vendor"},
                        "price": {"$first": "$line_items.price"},
                        "variant_title": {"$first": "$line_items.variant_title"}
                    }},
                    {"$sort": {"total_quantity": -1}},
                    {"$limit": 1}
                ]
            }}
        ]
        
        # Execute the combined pipeline
        combined_results = list(shOrders_collection.aggregate(combined_pipeline, allowDiskUse=True))
        
        if combined_results and len(combined_results) > 0:
            # Extract order count
            order_count_result = combined_results[0].get('order_count', [])
            results['last_24_hours_orders'] = order_count_result[0].get('count', 0) if order_count_result else 0
            
            # Extract total value
            total_value_result = combined_results[0].get('total_value', [])
            results['last_24_hours_value'] = total_value_result[0].get('value', 0) if total_value_result else 0
            
            # Extract top product
            top_product_result = combined_results[0].get('line_items', [])
            if top_product_result and len(top_product_result) > 0:
                results['top_product'] = {
                    'name': top_product_result[0]['_id'],
                    'quantity': top_product_result[0]['total_quantity'],
                    'vendor': top_product_result[0]['vendor'],
                    'price': top_product_result[0]['price'],
                    'variant': top_product_result[0].get('variant_title', ''),
                    'current_stock': 0  # Default value, will be updated below
                }
                
                # Find current stock level for top product with optimized query
                try:
                    product_name = results['top_product']['name']
                    
                    # Use a more efficient regex pattern and projection
                    product = shProducts_collection.find_one(
                        {
                            'username': username,
                            'title': {'$regex': f'^{re.escape(product_name)}', '$options': 'i'}
                        },
                        {'variants.inventory_quantity': 1, '_id': 0}
                    )
                    
                    # Calculate total inventory with a more efficient approach
                    if product and 'variants' in product:
                        # Use a list comprehension with conditional for better performance
                        inventory_quantities = [
                            variant.get('inventory_quantity', 0)
                            for variant in product['variants']
                            if 'inventory_quantity' in variant
                        ]
                        results['top_product']['current_stock'] = sum(inventory_quantities)
                except Exception as e:
                    logger.error(f"Error finding stock level for top product: {str(e)}")
                    if results['top_product']:
                        results['top_product']['current_stock'] = 0
    except Exception as e:
        logger.error(f"Error processing order data: {str(e)}")
        logger.error(traceback.format_exc())

    # Inventory stats removed to improve dashboard loading performance
    # No longer calling inventory APIs on dashboard load

    return results

@dashboard_bp.route('/dashboard')
@login_required
@subscription_required
@check_ebay_token
def dashboard():
    username = current_user.username
    logger.info(f"Generating dashboard for user: {username}")


    try:
        # Get cached user data (refreshed every 5 minutes)
        five_min_timestamp = datetime.now().replace(second=0, microsecond=0).timestamp() // 300
        user_data = get_cached_user_data(username, five_min_timestamp)

        # Get cached counts (refreshed every minute)
        one_min_timestamp = datetime.now().replace(second=0, microsecond=0).timestamp() // 60
        counts = get_cached_counts(username, one_min_timestamp)

        # Count open tickets
        is_admin = hasattr(current_user, 'roles') and 'Admin' in current_user.roles
        open_tickets_count = count_open_tickets(username, is_admin)

        # Get user's subscription status using User model
        user_obj = User.objects(username=username).first()
        if not user_obj:
            logger.error(f"User not found: {username}")
            return render_template('error.html', error_message="User not found")

        try:
            # Get subscription name using the model's method
            subscription_name = user_obj.get_subscription_name()
            logger.info(f"User {username} subscription: {subscription_name}")

            # Only Free users should see the free dashboard
            is_free_user = subscription_name == 'Free'

            # Ensure subscription is properly set on user object
            if subscription_name == 'Lifetime':
                try:
                    # Check if user already has a valid Lifetime subscription
                    if not user_obj.subscription or user_obj.subscription.name != 'Lifetime':
                        # Check if a Lifetime subscription exists in the database
                        subscription = Subscription.objects(name='Lifetime').first()
                        if not subscription:
                            subscription = Subscription(name='Lifetime').save()
                            logger.info(f"Created new Lifetime subscription for user {username}")
                        user_obj.subscription = subscription
                        user_obj.free_subscription = None
                        user_obj.save()
                        logger.info(f"Updated Lifetime subscription for user {username}")
                    else:
                        # Verify the subscription reference is valid
                        try:
                            subscription = Subscription.objects.get(id=user_obj.subscription.id)
                            if subscription.name != 'Lifetime':
                                # Fix incorrect subscription type
                                subscription = Subscription(name='Lifetime').save()
                                user_obj.subscription = subscription
                                user_obj.free_subscription = None
                                user_obj.save()
                                logger.info(f"Fixed incorrect subscription type for user {username}")
                        except DoesNotExist:
                            # Subscription reference is invalid, create new one
                            subscription = Subscription(name='Lifetime').save()
                            user_obj.subscription = subscription
                            user_obj.free_subscription = None
                            user_obj.save()
                            logger.info(f"Fixed invalid subscription reference for user {username}")
                except DoesNotExist:
                    # If there was an issue with the subscription reference, create a new one
                    subscription = Subscription(name='Lifetime').save()
                    user_obj.subscription = subscription
                    user_obj.free_subscription = None
                    user_obj.save()
                    logger.info(f"Created new Lifetime subscription for user {username}")
                except Exception as e:
                    logger.error(f"Error handling Lifetime subscription for user {username}: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Ensure user has at least a free subscription
                    user_obj.subscription = None
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Fallback to free subscription for user {username} after error")
            elif subscription_name == 'Free':
                # Ensure Free users have proper free_subscription data
                if not user_obj.free_subscription:
                    user_obj.subscription = None
                    user_obj.free_subscription = {'start_date': datetime.utcnow()}
                    user_obj.save()
                    logger.info(f"Set up free subscription for user {username}")
                elif user_obj.subscription:
                    # Clear any lingering subscription reference for Free users
                    user_obj.subscription = None
                    user_obj.save()
                    logger.info(f"Cleared lingering subscription for free user {username}")
        except Exception as e:
            logger.error(f"Error handling subscription for user {username}: {str(e)}")
            logger.error(traceback.format_exc())
            # Ensure user has at least a free subscription on error
            try:
                user_obj.subscription = None
                user_obj.free_subscription = {'start_date': datetime.utcnow()}
                user_obj.save()
                logger.info(f"Set up fallback free subscription for user {username} after error")
            except Exception as save_error:
                logger.error(f"Error saving fallback free subscription for user {username}: {str(save_error)}")
                logger.error(traceback.format_exc())

        return render_template(
            'dashboard.html',
            pending_buylist_orders=counts['pending_buylist_orders'],
            staged_count=counts['staged_count'],
            mobile_uploads_count=counts['mobile_uploads_count'],
            shopify_connected=user_data['shopify_connected'],
            auto_update_status=user_data['auto_update_status'],
            auto_pricing_status=user_data['auto_pricing_status'],
            current_user=user_obj,  # Pass the User model instance to the template
            is_free_user=is_free_user,  # Pass the free user status explicitly
            open_tickets_count=open_tickets_count,  # Pass the open tickets count
            last_24_hours_orders=counts['last_24_hours_orders'],  # Pass the 24h order count
            last_24_hours_value=counts['last_24_hours_value'],  # Pass the 24h order value
            top_product=counts['top_product'],  # Pass the top selling product
            get_currency_symbol=get_currency_symbol  # Pass the currency symbol function
        )

    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        logger.error(traceback.format_exc())
        return render_template('error.html', error_message="An unexpected error occurred while loading the dashboard.")

@dashboard_bp.route('/comics_scanning')
@login_required
@subscription_required
def comics_scanning():
    return render_template('comics_scanning.html')

@dashboard_bp.route('/sports_card_scanning')
@login_required
@subscription_required
def sports_card_scanning():
    return render_template('sports_card_scanning.html')

@dashboard_bp.route('/csv')
@login_required
@subscription_required
def csv_route():
    return render_template('csv.html')

@dashboard_bp.route('/shopify_autopricing')
@login_required
@subscription_required
def shopify_autopricing():
    return render_template('shopify_autopricing.html')

@dashboard_bp.route('/eu_purchasing')
@login_required
@subscription_required
def eu_purchasing():
    return render_template('eu_purchasing.html')

@dashboard_bp.route('/dashboard/api/top-sellers', methods=['GET'])
@login_required
@subscription_required
def get_top_sellers():
    username = current_user.username
    seven_days_ago = datetime.utcnow() - timedelta(days=7)

    pipeline = [
        {"$match": {"username": username, "created_at": {"$gte": seven_days_ago}}},
        {"$unwind": "$line_items"},
        {"$group": {
            "_id": "$line_items.vendor",
            "total_sales": {"$sum": "$line_items.quantity"}
        }},
        {"$sort": {"total_sales": -1}},
        {"$limit": 10}
    ]

    top_sellers = list(shOrders_collection.aggregate(pipeline))

    data = {
        "labels": [seller["_id"] for seller in top_sellers],
        "values": [seller["total_sales"] for seller in top_sellers]
    }

    return jsonify(data)

def identify_tcgplayer_id_column(reader):
    for field in reader.fieldnames:
        if all(re.match(r'^\d+$', str(row[field]).strip()) for row in reader if row[field].strip() and row[field].strip().lower() != 'unavailable'):
            return field
    return None

@dashboard_bp.route('/pos/convert_to_quicklist', methods=['POST'])
@login_required
@subscription_required
def convert_to_quicklist():
    if 'csv' not in request.files:
        logger.error('No file part')
        return jsonify({"error": "No file part"}), 400

    file = request.files['csv']
    logger.debug('Received file for conversion')

    try:
        file_contents = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        reader = csv.DictReader(file_contents)

        tcgplayer_id_column = identify_tcgplayer_id_column(reader)
        if not tcgplayer_id_column:
            logger.error('No suitable TCGplayer Id column found in the CSV file')
            return jsonify({"error": "No suitable TCGplayer Id column found in the CSV file"}), 400

        logger.info(f'Identified TCGplayer Id column: {tcgplayer_id_column}')

        output = io.StringIO()
        writer = csv.writer(output)

        combined_items = defaultdict(int)

        file_contents.seek(0)  # Reset the reader to the beginning of the file
        reader = csv.DictReader(file_contents)

        for row in reader:
            tcgplayer_id = row[tcgplayer_id_column].strip()
            if tcgplayer_id.isdigit():  # Only process rows with numeric TCGplayer Ids
                tcgplayer_id = int(tcgplayer_id)  # Convert to integer
                # Updated MongoDB query
                product = catalog_collection.find_one(
                    {"skus.skuId": int(tcgplayer_id)},
                    {"categoryId": 1}
                )

                if product and 'categoryId' in product:
                    category_id = product['categoryId']
                    combined_items[(category_id, tcgplayer_id)] += 1
                else:
                    logger.warning(f'No categoryId found for TCGplayer Id: {tcgplayer_id}')

        # Write the data without headers, maintaining the order
        for (category_id, tcgplayer_id), quantity in combined_items.items():
            writer.writerow([category_id, tcgplayer_id, quantity])

        output.seek(0)
        logger.debug('Conversion completed')

        return send_file(
            io.BytesIO(output.getvalue().encode()),
            mimetype='text/csv',
            as_attachment=True,
            download_name='quicklist.csv'
        )
    except Exception as e:
        logger.error(f'Error processing file: {e}')
        logger.error(traceback.format_exc())
        return jsonify({"error": f"Error processing file: {e}"}), 500

@dashboard_bp.route('/biggest_gainers')
@login_required
@subscription_required
def biggest_gainers():
    return render_template('biggest_gainers.html')

@dashboard_bp.route('/sales_summary')
@login_required
@subscription_required
def sales_summary():
    return render_template('sales_summary.html')

@dashboard_bp.route('/dashboard/chat', methods=['POST'])
@login_required
@subscription_required
def chat():
    username = current_user.username
    user_input = request.json.get('message', '')

    logger.info(f"Received chat request from user: {username}")

    # Get yesterday's date range
    yesterday = datetime.utcnow() - timedelta(days=1)
    start_yesterday = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
    end_yesterday = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

    # Prepare the messages for the OpenAI API
    messages = [
        {"role": "system", "content": f"""You are an AI assistant that helps generate MongoDB queries based on user questions about their order data.
        Always return a valid Python dictionary or list of dictionaries for aggregation pipelines.
        Use ISO format strings for date comparisons, not datetime objects or ISODate.
        Wrap your response in ```python ``` code blocks.
        Do not include any explanations outside the code blocks.
        Always include the username: '{username}' in the query to filter results for this specific user."""},
        {"role": "user", "content": f"""
Generate a MongoDB query to answer the following question about the user's order data:
User question: "{user_input}"
Username: {username}
Yesterday's date range: {start_yesterday.isoformat()} to {end_yesterday.isoformat()}

Remember to wrap your response in ```python ``` code blocks and only include the query, no explanations.
Use ISO format strings for date comparisons, not datetime objects or ISODate.
Ensure the query filters for the specific username: '{username}'.
"""}
    ]

    try:
        # Generate MongoDB query using OpenAI
        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt="\n".join([f"{msg['role']}: {msg['content']}" for msg in messages]),
            max_tokens=500,
            temperature=0.7
        )

        # Parse the AI-generated query
        ai_response = response.choices[0].text.strip()
        # Extract the Python code from the response
        code_match = re.search(r'```python\s*(.*?)\s*```', ai_response, re.DOTALL)
        if code_match:
            query_str = code_match.group(1)
        else:
            query_str = ai_response

        # Remove any leading/trailing whitespace and any print statements
        query_str = re.sub(r'^print\s*\(.*?\)$', '', query_str, flags=re.MULTILINE)
        query_str = query_str.strip()

        # Parse the query string manually
        query = parse_query_string(query_str)

        # Ensure the query includes the username filter
        if isinstance(query, dict) and 'username' not in query:
            query['username'] = username
        elif isinstance(query, list):
            if not any('username' in stage for stage in query if isinstance(stage, dict)):
                query.insert(0, {'$match': {'username': username}})

        logger.info(f"Final query generated for user: {username}")

    except Exception as e:
        logger.error(f"Error generating or parsing query: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"response": "I'm sorry, I couldn't generate a valid query. Could you please rephrase your question?"})

    # Execute the query
    try:
        if isinstance(query, list):  # Aggregation pipeline
            result = list(shOrders_collection.aggregate(query))
        else:  # Simple find query
            result = list(shOrders_collection.find(query))

        logger.debug(f"Query result: {result}")

        # Convert result to JSON-serializable format
        result_json = json.loads(json_util.dumps(result))

        # Use OpenAI to formulate a human-readable response
        result_messages = [
            {"role": "system", "content": "You are an AI assistant that helps interpret MongoDB query results and formulate clear, concise responses."},
            {"role": "user", "content": f"""
Given the user question "{user_input}" and the query result {result_json},
formulate a clear and concise response in natural language. If the result is empty, mention that no data was found for the given criteria.
"""}
        ]

        response = openai.Completion.create(
            engine="text-davinci-003",
            prompt="\n".join([f"{msg['role']}: {msg['content']}" for msg in result_messages]),
            max_tokens=500,
            temperature=0.7
        )

        return jsonify({"response": response.choices[0].text.strip()})

    except Exception as e:
        logger.error(f"Error executing query or generating response: {str(e)}")
        return jsonify({"response": f"I encountered an error while processing your request: {str(e)}"})

def parse_query_string(query_str):
    # Remove any trailing commas inside brackets or braces
    query_str = re.sub(r',(\s*[\]}])', r'\1', query_str)

    # Replace single quotes with double quotes for JSON compatibility
    query_str = query_str.replace("'", '"')

    # Remove any leading or trailing whitespace
    query_str = query_str.strip()

    # If the string starts with '[', assume it's a pipeline
    if query_str.startswith('['):
        try:
            return json.loads(query_str)
        except json.JSONDecodeError:
            pass  # If it fails, we'll try other methods

    # If the string starts with '{', assume it's a single query
    if query_str.startswith('{'):
        try:
            return json.loads(query_str)
        except json.JSONDecodeError:
            pass  # If it fails, we'll try other methods

    # If we reach here, try to parse it as a Python literal
    try:
        return ast.literal_eval(query_str)
    except (ValueError, SyntaxError):
        pass  # If it fails, we'll try one last method

    # As a last resort, try to evaluate it as Python code
    try:
        # This is potentially dangerous and should be used with caution
        local_vars = {}
        exec(f"query = {query_str}", globals(), local_vars)
        return local_vars['query']
    except Exception as e:
        logger.error(f"Failed to parse query string: {e}")
        logger.error(f"Problematic query string: {query_str}")
        raise ValueError("Could not parse query string")

def convert_datetime(obj):
    if isinstance(obj, dict):
        return {k: convert_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime(i) for i in obj]
    elif isinstance(obj, str) and obj.startswith('datetime'):
        return eval(obj)
    return obj

def parse_date_string(date_string):
    """
    Parse a date string into a datetime object.
    Supports various formats including ISO format and common date strings.
    """
    try:
        return datetime.fromisoformat(date_string)
    except ValueError:
        try:
            return datetime.strptime(date_string, "%Y-%m-%d")
        except ValueError:
            try:
                return datetime.strptime(date_string, "%m/%d/%Y")
            except ValueError:
                raise ValueError(f"Unable to parse date string: {date_string}")

def get_date_range(date_string):
    """
    Get a date range for a given date string.
    Supports 'today', 'yesterday', 'this week', 'last week', 'this month', 'last month'.
    Returns a tuple of (start_date, end_date).
    """
    today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    if date_string == 'today':
        return today, today.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif date_string == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    elif date_string == 'this week':
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6, hours=23, minutes=59, seconds=59, microseconds=999999)
        return start_of_week, end_of_week

# API endpoints for lazy loading dashboard tab content
@dashboard_bp.route('/api/dashboard/enterprise-content', methods=['GET'])
@login_required
@subscription_required
def get_enterprise_content():
    """
    API endpoint to get enterprise tab content for lazy loading
    """
    try:
        # For now, we'll just return a success response
        # In a real implementation, you might fetch specific data for this tab
        return jsonify({
            'success': True,
            'message': 'Enterprise content loaded successfully'
        })
    except Exception as e:
        logger.error(f"Error loading enterprise content: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': 'Error loading enterprise content'
        }), 500

@dashboard_bp.route('/api/dashboard/yesterday-content', methods=['GET'])
@login_required
@subscription_required
def get_yesterday_content():
    """
    API endpoint to get yesterday tab content for lazy loading
    """
    username = current_user.username
    
    try:
        # Get yesterday's date range
        yesterday = datetime.utcnow() - timedelta(days=1)
        start_yesterday = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_yesterday = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        last_24_hours_iso = start_yesterday.strftime('%Y-%m-%dT%H:%M:%S-00:00')
        
        # Count unique orders from last 24 hours (using id to avoid duplicates)
        unique_orders_pipeline = [
            {"$match": {"username": username, "created_at": {"$gte": last_24_hours_iso}}},
            {"$group": {"_id": "$id"}},  # Group by order ID to get unique orders
            {"$count": "unique_count"}
        ]

        unique_orders_result = list(shOrders_collection.aggregate(unique_orders_pipeline))
        last_24_hours_orders = 0
        if unique_orders_result and len(unique_orders_result) > 0:
            last_24_hours_orders = unique_orders_result[0].get('unique_count', 0)
            
        # Calculate total order value from last 24 hours (avoiding duplicates)
        orders_24h_pipeline = [
            {"$match": {"username": username, "created_at": {"$gte": last_24_hours_iso}}},
            {"$group": {
                "_id": "$id",  # Group by order ID to avoid duplicates
                "total_price": {"$first": {"$toDouble": "$total_price"}}
            }},
            {"$group": {
                "_id": None,
                "total_value": {"$sum": "$total_price"},
                "order_count": {"$sum": 1}
            }}
        ]

        orders_24h_result = list(shOrders_collection.aggregate(orders_24h_pipeline))
        orders_24h_value = 0
        if orders_24h_result and len(orders_24h_result) > 0:
            orders_24h_value = orders_24h_result[0].get('total_value', 0)
            
        # Find top selling product in last 24 hours (avoiding duplicate orders)
        top_product_pipeline = [
            {"$match": {"username": username, "created_at": {"$gte": last_24_hours_iso}}},
            {"$group": {
                "_id": "$id",  # Group by order ID to avoid duplicates
                "line_items": {"$first": "$line_items"}  # Take line items from first occurrence
            }},
            {"$unwind": "$line_items"},
            {"$group": {
                "_id": "$line_items.name",
                "total_quantity": {"$sum": "$line_items.quantity"},
                "vendor": {"$first": "$line_items.vendor"},
                "price": {"$first": "$line_items.price"},
                "variant_title": {"$first": "$line_items.variant_title"}
            }},
            {"$sort": {"total_quantity": -1}},
            {"$limit": 1}
        ]

        top_product_result = list(shOrders_collection.aggregate(top_product_pipeline))
        top_product = None
        if top_product_result and len(top_product_result) > 0:
            top_product = {
                'name': top_product_result[0]['_id'],
                'quantity': top_product_result[0]['total_quantity'],
                'vendor': top_product_result[0]['vendor'],
                'price': top_product_result[0]['price'],
                'variant': top_product_result[0].get('variant_title', '')
            }

            # Find current stock level for top product
            try:
                product_name = top_product['name']

                # Query shProducts collection to find the product
                product_query = {
                    'username': username,
                    'title': {'$regex': f'^{re.escape(product_name)}', '$options': 'i'}
                }

                # Project only the variants field
                product_projection = {'variants': 1, '_id': 0}

                # Find the product
                product = shProducts_collection.find_one(product_query, product_projection)

                # Calculate total inventory
                total_inventory = 0
                if product and 'variants' in product:
                    for variant in product['variants']:
                        if 'inventory_quantity' in variant:
                            total_inventory += variant.get('inventory_quantity', 0)

                # Add inventory to top_product
                top_product['current_stock'] = total_inventory
            except Exception as e:
                logger.error(f"Error finding stock level for top product: {str(e)}")
                top_product['current_stock'] = 0
                
        # Return the data as JSON
        return jsonify({
            'success': True,
            'data': {
                'last_24_hours_orders': last_24_hours_orders,
                'last_24_hours_value': orders_24h_value,
                'top_product': top_product
            }
        })
    except Exception as e:
        logger.error(f"Error loading yesterday content: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': 'Error loading yesterday content'
        }), 500
