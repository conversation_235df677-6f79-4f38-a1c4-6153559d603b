"""
API wrapper functions to provide compatibility between the JavaScript frontend
and the shopify_autopricing_routes.py backend.
"""

from flask import request, jsonify
from routes import shopify_autopricing_routes


def save_game_wrapper():
    """
    Wrapper for saving game settings that extracts the game name from request data
    and calls the appropriate save_game_rules function.
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Extract game name from the data
        game_name = data.get('name') or data.get('id')
        if not game_name:
            return jsonify({
                'success': False,
                'error': 'Game name is required'
            }), 400
        
        # Call the original save_game_rules function with the game name
        # We need to temporarily set the game_name in the URL path
        return shopify_autopricing_routes.save_game_rules(game_name)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error saving game settings: {str(e)}'
        }), 500


def get_price_type_preference_wrapper():
    """
    Wrapper to match the expected response format for price type preferences.
    """
    try:
        response = shopify_autopricing_routes.get_price_type_preference()
        data = response.get_json()
        
        if data and data.get('success'):
            # Transform the response to match what the JavaScript expects
            return jsonify({
                'success': True,
                'price_preference': data.get('price_type_order', [])
            })
        else:
            return response
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error getting price type preference: {str(e)}'
        }), 500


def save_price_type_preference_wrapper():
    """
    Wrapper to transform the request format for price type preferences.
    """
    try:
        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Transform the request to match what the backend expects
        transformed_data = {
            'price_type_order': data.get('price_preference', [])
        }
        
        # Temporarily replace the request data
        original_json = request.json
        request.json = transformed_data
        
        try:
            response = shopify_autopricing_routes.save_price_type_preference()
            return response
        finally:
            # Restore original request data
            request.json = original_json
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error saving price type preference: {str(e)}'
        }), 500
